// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        v6.31.1
// source: addr/v1/address.proto

package v1

import (
	common "halalplus/api/common"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"

	wrappers "github.com/golang/protobuf/ptypes/wrappers"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type AddressLevel int32

const (
	AddressLevel_LEVEL_UNSPECIFIED AddressLevel = 0 // 默认值（建议保留）
	AddressLevel_PROVINCE          AddressLevel = 1 // 省
	AddressLevel_CITY              AddressLevel = 2 // 市
	AddressLevel_DISTRICT          AddressLevel = 3 // 区/镇
	AddressLevel_VILLAGE           AddressLevel = 4 // 村/社区
)

// Enum value maps for AddressLevel.
var (
	AddressLevel_name = map[int32]string{
		0: "LEVEL_UNSPECIFIED",
		1: "PROVINCE",
		2: "CITY",
		3: "DISTRICT",
		4: "VILLAGE",
	}
	AddressLevel_value = map[string]int32{
		"LEVEL_UNSPECIFIED": 0,
		"PROVINCE":          1,
		"CITY":              2,
		"DISTRICT":          3,
		"VILLAGE":           4,
	}
)

func (x AddressLevel) Enum() *AddressLevel {
	p := new(AddressLevel)
	*p = x
	return p
}

func (x AddressLevel) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (AddressLevel) Descriptor() protoreflect.EnumDescriptor {
	return file_addr_v1_address_proto_enumTypes[0].Descriptor()
}

func (AddressLevel) Type() protoreflect.EnumType {
	return &file_addr_v1_address_proto_enumTypes[0]
}

func (x AddressLevel) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use AddressLevel.Descriptor instead.
func (AddressLevel) EnumDescriptor() ([]byte, []int) {
	return file_addr_v1_address_proto_rawDescGZIP(), []int{0}
}

// 根据经纬度查询地址请求
type GetAddressByLocationReq struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 纬度，例如 23.118869
	Latitude float64 `protobuf:"fixed64,1,opt,name=latitude,proto3" json:"latitude,omitempty" dc:"纬度，例如 23.118869"`
	// 经度，例如 113.370062
	Longitude     float64 `protobuf:"fixed64,2,opt,name=longitude,proto3" json:"longitude,omitempty" dc:"经度，例如 113.370062"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetAddressByLocationReq) Reset() {
	*x = GetAddressByLocationReq{}
	mi := &file_addr_v1_address_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetAddressByLocationReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetAddressByLocationReq) ProtoMessage() {}

func (x *GetAddressByLocationReq) ProtoReflect() protoreflect.Message {
	mi := &file_addr_v1_address_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetAddressByLocationReq.ProtoReflect.Descriptor instead.
func (*GetAddressByLocationReq) Descriptor() ([]byte, []int) {
	return file_addr_v1_address_proto_rawDescGZIP(), []int{0}
}

func (x *GetAddressByLocationReq) GetLatitude() float64 {
	if x != nil {
		return x.Latitude
	}
	return 0
}

func (x *GetAddressByLocationReq) GetLongitude() float64 {
	if x != nil {
		return x.Longitude
	}
	return 0
}

// 用户地址结构
type GetAddressByLocationResData struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 地址级联信息（省）
	Level1Code string `protobuf:"bytes,2,opt,name=level1_code,json=level1Code,proto3" json:"level1_code,omitempty" dc:"地址级联信息（省）"`
	Level1Name string `protobuf:"bytes,3,opt,name=level1_name,json=level1Name,proto3" json:"level1_name,omitempty"`
	// 地址级联信息（市）
	Level2Code string `protobuf:"bytes,4,opt,name=level2_code,json=level2Code,proto3" json:"level2_code,omitempty" dc:"地址级联信息（市）"`
	Level2Name string `protobuf:"bytes,5,opt,name=level2_name,json=level2Name,proto3" json:"level2_name,omitempty"`
	// 地址级联信息（区/镇）
	Level3Code string `protobuf:"bytes,6,opt,name=level3_code,json=level3Code,proto3" json:"level3_code,omitempty" dc:"地址级联信息（区/镇）"`
	Level3Name string `protobuf:"bytes,7,opt,name=level3_name,json=level3Name,proto3" json:"level3_name,omitempty"`
	// 地址级联信息（村/社区）
	Level4Code string `protobuf:"bytes,8,opt,name=level4_code,json=level4Code,proto3" json:"level4_code,omitempty" dc:"地址级联信息（村/社区）"`
	Level4Name string `protobuf:"bytes,9,opt,name=level4_name,json=level4Name,proto3" json:"level4_name,omitempty"`
	// 详细地址（门牌号）
	Address       string `protobuf:"bytes,10,opt,name=address,proto3" json:"address,omitempty" dc:"详细地址（门牌号）"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetAddressByLocationResData) Reset() {
	*x = GetAddressByLocationResData{}
	mi := &file_addr_v1_address_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetAddressByLocationResData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetAddressByLocationResData) ProtoMessage() {}

func (x *GetAddressByLocationResData) ProtoReflect() protoreflect.Message {
	mi := &file_addr_v1_address_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetAddressByLocationResData.ProtoReflect.Descriptor instead.
func (*GetAddressByLocationResData) Descriptor() ([]byte, []int) {
	return file_addr_v1_address_proto_rawDescGZIP(), []int{1}
}

func (x *GetAddressByLocationResData) GetLevel1Code() string {
	if x != nil {
		return x.Level1Code
	}
	return ""
}

func (x *GetAddressByLocationResData) GetLevel1Name() string {
	if x != nil {
		return x.Level1Name
	}
	return ""
}

func (x *GetAddressByLocationResData) GetLevel2Code() string {
	if x != nil {
		return x.Level2Code
	}
	return ""
}

func (x *GetAddressByLocationResData) GetLevel2Name() string {
	if x != nil {
		return x.Level2Name
	}
	return ""
}

func (x *GetAddressByLocationResData) GetLevel3Code() string {
	if x != nil {
		return x.Level3Code
	}
	return ""
}

func (x *GetAddressByLocationResData) GetLevel3Name() string {
	if x != nil {
		return x.Level3Name
	}
	return ""
}

func (x *GetAddressByLocationResData) GetLevel4Code() string {
	if x != nil {
		return x.Level4Code
	}
	return ""
}

func (x *GetAddressByLocationResData) GetLevel4Name() string {
	if x != nil {
		return x.Level4Name
	}
	return ""
}

func (x *GetAddressByLocationResData) GetAddress() string {
	if x != nil {
		return x.Address
	}
	return ""
}

type GetAddressByLocationRes struct {
	state         protoimpl.MessageState       `protogen:"open.v1"`
	Code          int32                        `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`
	Msg           string                       `protobuf:"bytes,2,opt,name=msg,proto3" json:"msg,omitempty"`
	Error         *common.Error                `protobuf:"bytes,3,opt,name=error,proto3" json:"error,omitempty"`
	Data          *GetAddressByLocationResData `protobuf:"bytes,4,opt,name=data,proto3" json:"data,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetAddressByLocationRes) Reset() {
	*x = GetAddressByLocationRes{}
	mi := &file_addr_v1_address_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetAddressByLocationRes) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetAddressByLocationRes) ProtoMessage() {}

func (x *GetAddressByLocationRes) ProtoReflect() protoreflect.Message {
	mi := &file_addr_v1_address_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetAddressByLocationRes.ProtoReflect.Descriptor instead.
func (*GetAddressByLocationRes) Descriptor() ([]byte, []int) {
	return file_addr_v1_address_proto_rawDescGZIP(), []int{2}
}

func (x *GetAddressByLocationRes) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *GetAddressByLocationRes) GetMsg() string {
	if x != nil {
		return x.Msg
	}
	return ""
}

func (x *GetAddressByLocationRes) GetError() *common.Error {
	if x != nil {
		return x.Error
	}
	return nil
}

func (x *GetAddressByLocationRes) GetData() *GetAddressByLocationResData {
	if x != nil {
		return x.Data
	}
	return nil
}

type GetSubRegionsReq struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	ParentCode    string                 `protobuf:"bytes,1,opt,name=parent_code,json=parentCode,proto3" json:"parent_code,omitempty" dc:"父级编码，空则返回顶级区域"` // 父级编码，空则返回顶级区域
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetSubRegionsReq) Reset() {
	*x = GetSubRegionsReq{}
	mi := &file_addr_v1_address_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetSubRegionsReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetSubRegionsReq) ProtoMessage() {}

func (x *GetSubRegionsReq) ProtoReflect() protoreflect.Message {
	mi := &file_addr_v1_address_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetSubRegionsReq.ProtoReflect.Descriptor instead.
func (*GetSubRegionsReq) Descriptor() ([]byte, []int) {
	return file_addr_v1_address_proto_rawDescGZIP(), []int{3}
}

func (x *GetSubRegionsReq) GetParentCode() string {
	if x != nil {
		return x.ParentCode
	}
	return ""
}

// === 印尼地址 ===
// 数据来源 github.com/erlange/Kodepos-Wilayah-Indonesia
type AddressItem struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Code          string                 `protobuf:"bytes,1,opt,name=code,proto3" json:"code,omitempty" dc:"区划代码,唯一"`                            // 区划代码,唯一
	Name          string                 `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty" dc:"区划名称"`                               // 区划名称
	ParentCode    string                 `protobuf:"bytes,3,opt,name=parent_code,json=parentCode,proto3" json:"parent_code,omitempty" dc:"父级代码"` // 父级代码
	Level         AddressLevel           `protobuf:"varint,4,opt,name=level,proto3,enum=addr.v1.AddressLevel" json:"level,omitempty" dc:"层级"`    // 层级
	ZipCode       string                 `protobuf:"bytes,5,opt,name=zip_code,json=zipCode,proto3" json:"zip_code,omitempty" dc:"邮编（可为空）"`       // 邮编（可为空）
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *AddressItem) Reset() {
	*x = AddressItem{}
	mi := &file_addr_v1_address_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *AddressItem) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AddressItem) ProtoMessage() {}

func (x *AddressItem) ProtoReflect() protoreflect.Message {
	mi := &file_addr_v1_address_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AddressItem.ProtoReflect.Descriptor instead.
func (*AddressItem) Descriptor() ([]byte, []int) {
	return file_addr_v1_address_proto_rawDescGZIP(), []int{4}
}

func (x *AddressItem) GetCode() string {
	if x != nil {
		return x.Code
	}
	return ""
}

func (x *AddressItem) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *AddressItem) GetParentCode() string {
	if x != nil {
		return x.ParentCode
	}
	return ""
}

func (x *AddressItem) GetLevel() AddressLevel {
	if x != nil {
		return x.Level
	}
	return AddressLevel_LEVEL_UNSPECIFIED
}

func (x *AddressItem) GetZipCode() string {
	if x != nil {
		return x.ZipCode
	}
	return ""
}

type GetSubRegionsResData struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	List          []*AddressItem         `protobuf:"bytes,1,rep,name=list,proto3" json:"list,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetSubRegionsResData) Reset() {
	*x = GetSubRegionsResData{}
	mi := &file_addr_v1_address_proto_msgTypes[5]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetSubRegionsResData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetSubRegionsResData) ProtoMessage() {}

func (x *GetSubRegionsResData) ProtoReflect() protoreflect.Message {
	mi := &file_addr_v1_address_proto_msgTypes[5]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetSubRegionsResData.ProtoReflect.Descriptor instead.
func (*GetSubRegionsResData) Descriptor() ([]byte, []int) {
	return file_addr_v1_address_proto_rawDescGZIP(), []int{5}
}

func (x *GetSubRegionsResData) GetList() []*AddressItem {
	if x != nil {
		return x.List
	}
	return nil
}

type GetSubRegionsRes struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Code          int32                  `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`
	Msg           string                 `protobuf:"bytes,2,opt,name=msg,proto3" json:"msg,omitempty"`
	Error         *common.Error          `protobuf:"bytes,3,opt,name=error,proto3" json:"error,omitempty"`
	Data          *GetSubRegionsResData  `protobuf:"bytes,4,opt,name=data,proto3" json:"data,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetSubRegionsRes) Reset() {
	*x = GetSubRegionsRes{}
	mi := &file_addr_v1_address_proto_msgTypes[6]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetSubRegionsRes) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetSubRegionsRes) ProtoMessage() {}

func (x *GetSubRegionsRes) ProtoReflect() protoreflect.Message {
	mi := &file_addr_v1_address_proto_msgTypes[6]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetSubRegionsRes.ProtoReflect.Descriptor instead.
func (*GetSubRegionsRes) Descriptor() ([]byte, []int) {
	return file_addr_v1_address_proto_rawDescGZIP(), []int{6}
}

func (x *GetSubRegionsRes) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *GetSubRegionsRes) GetMsg() string {
	if x != nil {
		return x.Msg
	}
	return ""
}

func (x *GetSubRegionsRes) GetError() *common.Error {
	if x != nil {
		return x.Error
	}
	return nil
}

func (x *GetSubRegionsRes) GetData() *GetSubRegionsResData {
	if x != nil {
		return x.Data
	}
	return nil
}

// 用户地址结构
type UserAddress struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 地址ID（主键）
	Id uint64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty" dc:"地址ID（主键）"`
	// 地址级联信息（省）
	Level1Code string `protobuf:"bytes,2,opt,name=level1_code,json=level1Code,proto3" json:"level1_code,omitempty" dc:"地址级联信息（省）"`
	Level1Name string `protobuf:"bytes,3,opt,name=level1_name,json=level1Name,proto3" json:"level1_name,omitempty"`
	// 地址级联信息（市）
	Level2Code string `protobuf:"bytes,4,opt,name=level2_code,json=level2Code,proto3" json:"level2_code,omitempty" dc:"地址级联信息（市）"`
	Level2Name string `protobuf:"bytes,5,opt,name=level2_name,json=level2Name,proto3" json:"level2_name,omitempty"`
	// 地址级联信息（区/镇）
	Level3Code string `protobuf:"bytes,6,opt,name=level3_code,json=level3Code,proto3" json:"level3_code,omitempty" dc:"地址级联信息（区/镇）"`
	Level3Name string `protobuf:"bytes,7,opt,name=level3_name,json=level3Name,proto3" json:"level3_name,omitempty"`
	// 地址级联信息（村/社区）
	Level4Code string `protobuf:"bytes,8,opt,name=level4_code,json=level4Code,proto3" json:"level4_code,omitempty" dc:"地址级联信息（村/社区）"`
	Level4Name string `protobuf:"bytes,9,opt,name=level4_name,json=level4Name,proto3" json:"level4_name,omitempty"`
	// 详细地址（门牌号）
	Address string `protobuf:"bytes,10,opt,name=address,proto3" json:"address,omitempty" dc:"详细地址（门牌号）"`
	// 电话的国家区号
	AreaCode string `protobuf:"bytes,11,opt,name=area_code,json=areaCode,proto3" json:"area_code,omitempty" dc:"电话的国家区号"`
	// 手机号码
	PhoneNum string `protobuf:"bytes,12,opt,name=phone_num,json=phoneNum,proto3" json:"phone_num,omitempty" dc:"手机号码"`
	// 收货人姓名
	Receiver string `protobuf:"bytes,13,opt,name=receiver,proto3" json:"receiver,omitempty" dc:"收货人姓名"`
	// 是否默认地址
	IsDefault     bool `protobuf:"varint,14,opt,name=is_default,json=isDefault,proto3" json:"is_default,omitempty" dc:"是否默认地址"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *UserAddress) Reset() {
	*x = UserAddress{}
	mi := &file_addr_v1_address_proto_msgTypes[7]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UserAddress) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UserAddress) ProtoMessage() {}

func (x *UserAddress) ProtoReflect() protoreflect.Message {
	mi := &file_addr_v1_address_proto_msgTypes[7]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UserAddress.ProtoReflect.Descriptor instead.
func (*UserAddress) Descriptor() ([]byte, []int) {
	return file_addr_v1_address_proto_rawDescGZIP(), []int{7}
}

func (x *UserAddress) GetId() uint64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *UserAddress) GetLevel1Code() string {
	if x != nil {
		return x.Level1Code
	}
	return ""
}

func (x *UserAddress) GetLevel1Name() string {
	if x != nil {
		return x.Level1Name
	}
	return ""
}

func (x *UserAddress) GetLevel2Code() string {
	if x != nil {
		return x.Level2Code
	}
	return ""
}

func (x *UserAddress) GetLevel2Name() string {
	if x != nil {
		return x.Level2Name
	}
	return ""
}

func (x *UserAddress) GetLevel3Code() string {
	if x != nil {
		return x.Level3Code
	}
	return ""
}

func (x *UserAddress) GetLevel3Name() string {
	if x != nil {
		return x.Level3Name
	}
	return ""
}

func (x *UserAddress) GetLevel4Code() string {
	if x != nil {
		return x.Level4Code
	}
	return ""
}

func (x *UserAddress) GetLevel4Name() string {
	if x != nil {
		return x.Level4Name
	}
	return ""
}

func (x *UserAddress) GetAddress() string {
	if x != nil {
		return x.Address
	}
	return ""
}

func (x *UserAddress) GetAreaCode() string {
	if x != nil {
		return x.AreaCode
	}
	return ""
}

func (x *UserAddress) GetPhoneNum() string {
	if x != nil {
		return x.PhoneNum
	}
	return ""
}

func (x *UserAddress) GetReceiver() string {
	if x != nil {
		return x.Receiver
	}
	return ""
}

func (x *UserAddress) GetIsDefault() bool {
	if x != nil {
		return x.IsDefault
	}
	return false
}

// 获取地址列表请求
type GetAddressListReq struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 不设置时标识获取所有
	// 是否筛选默认地址（不传表示全部）
	IsDefault     *wrappers.BoolValue `protobuf:"bytes,1,opt,name=is_default,json=isDefault,proto3" json:"is_default,omitempty" dc:"不设置时标识获取所有是否筛选默认地址（不传表示全部）"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetAddressListReq) Reset() {
	*x = GetAddressListReq{}
	mi := &file_addr_v1_address_proto_msgTypes[8]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetAddressListReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetAddressListReq) ProtoMessage() {}

func (x *GetAddressListReq) ProtoReflect() protoreflect.Message {
	mi := &file_addr_v1_address_proto_msgTypes[8]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetAddressListReq.ProtoReflect.Descriptor instead.
func (*GetAddressListReq) Descriptor() ([]byte, []int) {
	return file_addr_v1_address_proto_rawDescGZIP(), []int{8}
}

func (x *GetAddressListReq) GetIsDefault() *wrappers.BoolValue {
	if x != nil {
		return x.IsDefault
	}
	return nil
}

type GetAddressListResData struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	List          []*UserAddress         `protobuf:"bytes,1,rep,name=list,proto3" json:"list,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetAddressListResData) Reset() {
	*x = GetAddressListResData{}
	mi := &file_addr_v1_address_proto_msgTypes[9]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetAddressListResData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetAddressListResData) ProtoMessage() {}

func (x *GetAddressListResData) ProtoReflect() protoreflect.Message {
	mi := &file_addr_v1_address_proto_msgTypes[9]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetAddressListResData.ProtoReflect.Descriptor instead.
func (*GetAddressListResData) Descriptor() ([]byte, []int) {
	return file_addr_v1_address_proto_rawDescGZIP(), []int{9}
}

func (x *GetAddressListResData) GetList() []*UserAddress {
	if x != nil {
		return x.List
	}
	return nil
}

// 地址列表返回
type GetAddressListRes struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Code          int32                  `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`
	Msg           string                 `protobuf:"bytes,2,opt,name=msg,proto3" json:"msg,omitempty"`
	Error         *common.Error          `protobuf:"bytes,3,opt,name=error,proto3" json:"error,omitempty"`
	Data          *GetAddressListResData `protobuf:"bytes,4,opt,name=data,proto3" json:"data,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetAddressListRes) Reset() {
	*x = GetAddressListRes{}
	mi := &file_addr_v1_address_proto_msgTypes[10]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetAddressListRes) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetAddressListRes) ProtoMessage() {}

func (x *GetAddressListRes) ProtoReflect() protoreflect.Message {
	mi := &file_addr_v1_address_proto_msgTypes[10]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetAddressListRes.ProtoReflect.Descriptor instead.
func (*GetAddressListRes) Descriptor() ([]byte, []int) {
	return file_addr_v1_address_proto_rawDescGZIP(), []int{10}
}

func (x *GetAddressListRes) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *GetAddressListRes) GetMsg() string {
	if x != nil {
		return x.Msg
	}
	return ""
}

func (x *GetAddressListRes) GetError() *common.Error {
	if x != nil {
		return x.Error
	}
	return nil
}

func (x *GetAddressListRes) GetData() *GetAddressListResData {
	if x != nil {
		return x.Data
	}
	return nil
}

// 添加或更新地址的请求
type SaveAddressReq struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 地址ID（主键），没有id则是新增
	Id uint64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty" dc:"地址ID（主键），没有id则是新增"`
	// 地址级联信息（省）
	Level1Code string `protobuf:"bytes,2,opt,name=level1_code,json=level1Code,proto3" json:"level1_code,omitempty" dc:"地址级联信息（省）"`
	Level1Name string `protobuf:"bytes,3,opt,name=level1_name,json=level1Name,proto3" json:"level1_name,omitempty"`
	// 地址级联信息（市）
	Level2Code string `protobuf:"bytes,4,opt,name=level2_code,json=level2Code,proto3" json:"level2_code,omitempty" dc:"地址级联信息（市）"`
	Level2Name string `protobuf:"bytes,5,opt,name=level2_name,json=level2Name,proto3" json:"level2_name,omitempty"`
	// 地址级联信息（区/镇）
	Level3Code string `protobuf:"bytes,6,opt,name=level3_code,json=level3Code,proto3" json:"level3_code,omitempty" dc:"地址级联信息（区/镇）"`
	Level3Name string `protobuf:"bytes,7,opt,name=level3_name,json=level3Name,proto3" json:"level3_name,omitempty"`
	// 地址级联信息（村/社区）
	Level4Code string `protobuf:"bytes,8,opt,name=level4_code,json=level4Code,proto3" json:"level4_code,omitempty" dc:"地址级联信息（村/社区）"`
	Level4Name string `protobuf:"bytes,9,opt,name=level4_name,json=level4Name,proto3" json:"level4_name,omitempty"`
	// 详细地址（门牌号）
	Address string `protobuf:"bytes,10,opt,name=address,proto3" json:"address,omitempty" dc:"详细地址（门牌号）"`
	// 电话的国家区号
	AreaCode string `protobuf:"bytes,11,opt,name=area_code,json=areaCode,proto3" json:"area_code,omitempty" dc:"电话的国家区号"`
	// 手机号码
	PhoneNum string `protobuf:"bytes,12,opt,name=phone_num,json=phoneNum,proto3" json:"phone_num,omitempty" dc:"手机号码"`
	// 收货人姓名
	Receiver string `protobuf:"bytes,13,opt,name=receiver,proto3" json:"receiver,omitempty" dc:"收货人姓名"`
	// 是否默认地址
	IsDefault     *wrappers.BoolValue `protobuf:"bytes,14,opt,name=is_default,json=isDefault,proto3" json:"is_default,omitempty" dc:"是否默认地址"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *SaveAddressReq) Reset() {
	*x = SaveAddressReq{}
	mi := &file_addr_v1_address_proto_msgTypes[11]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SaveAddressReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SaveAddressReq) ProtoMessage() {}

func (x *SaveAddressReq) ProtoReflect() protoreflect.Message {
	mi := &file_addr_v1_address_proto_msgTypes[11]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SaveAddressReq.ProtoReflect.Descriptor instead.
func (*SaveAddressReq) Descriptor() ([]byte, []int) {
	return file_addr_v1_address_proto_rawDescGZIP(), []int{11}
}

func (x *SaveAddressReq) GetId() uint64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *SaveAddressReq) GetLevel1Code() string {
	if x != nil {
		return x.Level1Code
	}
	return ""
}

func (x *SaveAddressReq) GetLevel1Name() string {
	if x != nil {
		return x.Level1Name
	}
	return ""
}

func (x *SaveAddressReq) GetLevel2Code() string {
	if x != nil {
		return x.Level2Code
	}
	return ""
}

func (x *SaveAddressReq) GetLevel2Name() string {
	if x != nil {
		return x.Level2Name
	}
	return ""
}

func (x *SaveAddressReq) GetLevel3Code() string {
	if x != nil {
		return x.Level3Code
	}
	return ""
}

func (x *SaveAddressReq) GetLevel3Name() string {
	if x != nil {
		return x.Level3Name
	}
	return ""
}

func (x *SaveAddressReq) GetLevel4Code() string {
	if x != nil {
		return x.Level4Code
	}
	return ""
}

func (x *SaveAddressReq) GetLevel4Name() string {
	if x != nil {
		return x.Level4Name
	}
	return ""
}

func (x *SaveAddressReq) GetAddress() string {
	if x != nil {
		return x.Address
	}
	return ""
}

func (x *SaveAddressReq) GetAreaCode() string {
	if x != nil {
		return x.AreaCode
	}
	return ""
}

func (x *SaveAddressReq) GetPhoneNum() string {
	if x != nil {
		return x.PhoneNum
	}
	return ""
}

func (x *SaveAddressReq) GetReceiver() string {
	if x != nil {
		return x.Receiver
	}
	return ""
}

func (x *SaveAddressReq) GetIsDefault() *wrappers.BoolValue {
	if x != nil {
		return x.IsDefault
	}
	return nil
}

type SaveAddressRes struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Code          int32                  `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`
	Msg           string                 `protobuf:"bytes,2,opt,name=msg,proto3" json:"msg,omitempty"`
	Error         *common.Error          `protobuf:"bytes,3,opt,name=error,proto3" json:"error,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *SaveAddressRes) Reset() {
	*x = SaveAddressRes{}
	mi := &file_addr_v1_address_proto_msgTypes[12]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SaveAddressRes) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SaveAddressRes) ProtoMessage() {}

func (x *SaveAddressRes) ProtoReflect() protoreflect.Message {
	mi := &file_addr_v1_address_proto_msgTypes[12]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SaveAddressRes.ProtoReflect.Descriptor instead.
func (*SaveAddressRes) Descriptor() ([]byte, []int) {
	return file_addr_v1_address_proto_rawDescGZIP(), []int{12}
}

func (x *SaveAddressRes) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *SaveAddressRes) GetMsg() string {
	if x != nil {
		return x.Msg
	}
	return ""
}

func (x *SaveAddressRes) GetError() *common.Error {
	if x != nil {
		return x.Error
	}
	return nil
}

type DeleteAddressReq struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            uint64                 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DeleteAddressReq) Reset() {
	*x = DeleteAddressReq{}
	mi := &file_addr_v1_address_proto_msgTypes[13]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DeleteAddressReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteAddressReq) ProtoMessage() {}

func (x *DeleteAddressReq) ProtoReflect() protoreflect.Message {
	mi := &file_addr_v1_address_proto_msgTypes[13]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteAddressReq.ProtoReflect.Descriptor instead.
func (*DeleteAddressReq) Descriptor() ([]byte, []int) {
	return file_addr_v1_address_proto_rawDescGZIP(), []int{13}
}

func (x *DeleteAddressReq) GetId() uint64 {
	if x != nil {
		return x.Id
	}
	return 0
}

// 删除地址
type DeleteAddressRes struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Code          int32                  `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`
	Msg           string                 `protobuf:"bytes,2,opt,name=msg,proto3" json:"msg,omitempty"`
	Error         *common.Error          `protobuf:"bytes,3,opt,name=error,proto3" json:"error,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DeleteAddressRes) Reset() {
	*x = DeleteAddressRes{}
	mi := &file_addr_v1_address_proto_msgTypes[14]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DeleteAddressRes) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteAddressRes) ProtoMessage() {}

func (x *DeleteAddressRes) ProtoReflect() protoreflect.Message {
	mi := &file_addr_v1_address_proto_msgTypes[14]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteAddressRes.ProtoReflect.Descriptor instead.
func (*DeleteAddressRes) Descriptor() ([]byte, []int) {
	return file_addr_v1_address_proto_rawDescGZIP(), []int{14}
}

func (x *DeleteAddressRes) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *DeleteAddressRes) GetMsg() string {
	if x != nil {
		return x.Msg
	}
	return ""
}

func (x *DeleteAddressRes) GetError() *common.Error {
	if x != nil {
		return x.Error
	}
	return nil
}

var File_addr_v1_address_proto protoreflect.FileDescriptor

const file_addr_v1_address_proto_rawDesc = "" +
	"\n" +
	"\x15addr/v1/address.proto\x12\aaddr.v1\x1a\x1egoogle/protobuf/wrappers.proto\x1a\x11common/base.proto\"S\n" +
	"\x17GetAddressByLocationReq\x12\x1a\n" +
	"\blatitude\x18\x01 \x01(\x01R\blatitude\x12\x1c\n" +
	"\tlongitude\x18\x02 \x01(\x01R\tlongitude\"\xbf\x02\n" +
	"\x1bGetAddressByLocationResData\x12\x1f\n" +
	"\vlevel1_code\x18\x02 \x01(\tR\n" +
	"level1Code\x12\x1f\n" +
	"\vlevel1_name\x18\x03 \x01(\tR\n" +
	"level1Name\x12\x1f\n" +
	"\vlevel2_code\x18\x04 \x01(\tR\n" +
	"level2Code\x12\x1f\n" +
	"\vlevel2_name\x18\x05 \x01(\tR\n" +
	"level2Name\x12\x1f\n" +
	"\vlevel3_code\x18\x06 \x01(\tR\n" +
	"level3Code\x12\x1f\n" +
	"\vlevel3_name\x18\a \x01(\tR\n" +
	"level3Name\x12\x1f\n" +
	"\vlevel4_code\x18\b \x01(\tR\n" +
	"level4Code\x12\x1f\n" +
	"\vlevel4_name\x18\t \x01(\tR\n" +
	"level4Name\x12\x18\n" +
	"\aaddress\x18\n" +
	" \x01(\tR\aaddress\"\x9e\x01\n" +
	"\x17GetAddressByLocationRes\x12\x12\n" +
	"\x04code\x18\x01 \x01(\x05R\x04code\x12\x10\n" +
	"\x03msg\x18\x02 \x01(\tR\x03msg\x12#\n" +
	"\x05error\x18\x03 \x01(\v2\r.common.ErrorR\x05error\x128\n" +
	"\x04data\x18\x04 \x01(\v2$.addr.v1.GetAddressByLocationResDataR\x04data\"3\n" +
	"\x10GetSubRegionsReq\x12\x1f\n" +
	"\vparent_code\x18\x01 \x01(\tR\n" +
	"parentCode\"\x9e\x01\n" +
	"\vAddressItem\x12\x12\n" +
	"\x04code\x18\x01 \x01(\tR\x04code\x12\x12\n" +
	"\x04name\x18\x02 \x01(\tR\x04name\x12\x1f\n" +
	"\vparent_code\x18\x03 \x01(\tR\n" +
	"parentCode\x12+\n" +
	"\x05level\x18\x04 \x01(\x0e2\x15.addr.v1.AddressLevelR\x05level\x12\x19\n" +
	"\bzip_code\x18\x05 \x01(\tR\azipCode\"@\n" +
	"\x14GetSubRegionsResData\x12(\n" +
	"\x04list\x18\x01 \x03(\v2\x14.addr.v1.AddressItemR\x04list\"\x90\x01\n" +
	"\x10GetSubRegionsRes\x12\x12\n" +
	"\x04code\x18\x01 \x01(\x05R\x04code\x12\x10\n" +
	"\x03msg\x18\x02 \x01(\tR\x03msg\x12#\n" +
	"\x05error\x18\x03 \x01(\v2\r.common.ErrorR\x05error\x121\n" +
	"\x04data\x18\x04 \x01(\v2\x1d.addr.v1.GetSubRegionsResDataR\x04data\"\xb4\x03\n" +
	"\vUserAddress\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\x04R\x02id\x12\x1f\n" +
	"\vlevel1_code\x18\x02 \x01(\tR\n" +
	"level1Code\x12\x1f\n" +
	"\vlevel1_name\x18\x03 \x01(\tR\n" +
	"level1Name\x12\x1f\n" +
	"\vlevel2_code\x18\x04 \x01(\tR\n" +
	"level2Code\x12\x1f\n" +
	"\vlevel2_name\x18\x05 \x01(\tR\n" +
	"level2Name\x12\x1f\n" +
	"\vlevel3_code\x18\x06 \x01(\tR\n" +
	"level3Code\x12\x1f\n" +
	"\vlevel3_name\x18\a \x01(\tR\n" +
	"level3Name\x12\x1f\n" +
	"\vlevel4_code\x18\b \x01(\tR\n" +
	"level4Code\x12\x1f\n" +
	"\vlevel4_name\x18\t \x01(\tR\n" +
	"level4Name\x12\x18\n" +
	"\aaddress\x18\n" +
	" \x01(\tR\aaddress\x12\x1b\n" +
	"\tarea_code\x18\v \x01(\tR\bareaCode\x12\x1b\n" +
	"\tphone_num\x18\f \x01(\tR\bphoneNum\x12\x1a\n" +
	"\breceiver\x18\r \x01(\tR\breceiver\x12\x1d\n" +
	"\n" +
	"is_default\x18\x0e \x01(\bR\tisDefault\"N\n" +
	"\x11GetAddressListReq\x129\n" +
	"\n" +
	"is_default\x18\x01 \x01(\v2\x1a.google.protobuf.BoolValueR\tisDefault\"A\n" +
	"\x15GetAddressListResData\x12(\n" +
	"\x04list\x18\x01 \x03(\v2\x14.addr.v1.UserAddressR\x04list\"\x92\x01\n" +
	"\x11GetAddressListRes\x12\x12\n" +
	"\x04code\x18\x01 \x01(\x05R\x04code\x12\x10\n" +
	"\x03msg\x18\x02 \x01(\tR\x03msg\x12#\n" +
	"\x05error\x18\x03 \x01(\v2\r.common.ErrorR\x05error\x122\n" +
	"\x04data\x18\x04 \x01(\v2\x1e.addr.v1.GetAddressListResDataR\x04data\"\xd3\x03\n" +
	"\x0eSaveAddressReq\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\x04R\x02id\x12\x1f\n" +
	"\vlevel1_code\x18\x02 \x01(\tR\n" +
	"level1Code\x12\x1f\n" +
	"\vlevel1_name\x18\x03 \x01(\tR\n" +
	"level1Name\x12\x1f\n" +
	"\vlevel2_code\x18\x04 \x01(\tR\n" +
	"level2Code\x12\x1f\n" +
	"\vlevel2_name\x18\x05 \x01(\tR\n" +
	"level2Name\x12\x1f\n" +
	"\vlevel3_code\x18\x06 \x01(\tR\n" +
	"level3Code\x12\x1f\n" +
	"\vlevel3_name\x18\a \x01(\tR\n" +
	"level3Name\x12\x1f\n" +
	"\vlevel4_code\x18\b \x01(\tR\n" +
	"level4Code\x12\x1f\n" +
	"\vlevel4_name\x18\t \x01(\tR\n" +
	"level4Name\x12\x18\n" +
	"\aaddress\x18\n" +
	" \x01(\tR\aaddress\x12\x1b\n" +
	"\tarea_code\x18\v \x01(\tR\bareaCode\x12\x1b\n" +
	"\tphone_num\x18\f \x01(\tR\bphoneNum\x12\x1a\n" +
	"\breceiver\x18\r \x01(\tR\breceiver\x129\n" +
	"\n" +
	"is_default\x18\x0e \x01(\v2\x1a.google.protobuf.BoolValueR\tisDefault\"[\n" +
	"\x0eSaveAddressRes\x12\x12\n" +
	"\x04code\x18\x01 \x01(\x05R\x04code\x12\x10\n" +
	"\x03msg\x18\x02 \x01(\tR\x03msg\x12#\n" +
	"\x05error\x18\x03 \x01(\v2\r.common.ErrorR\x05error\"\"\n" +
	"\x10DeleteAddressReq\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\x04R\x02id\"]\n" +
	"\x10DeleteAddressRes\x12\x12\n" +
	"\x04code\x18\x01 \x01(\x05R\x04code\x12\x10\n" +
	"\x03msg\x18\x02 \x01(\tR\x03msg\x12#\n" +
	"\x05error\x18\x03 \x01(\v2\r.common.ErrorR\x05error*X\n" +
	"\fAddressLevel\x12\x15\n" +
	"\x11LEVEL_UNSPECIFIED\x10\x00\x12\f\n" +
	"\bPROVINCE\x10\x01\x12\b\n" +
	"\x04CITY\x10\x02\x12\f\n" +
	"\bDISTRICT\x10\x03\x12\v\n" +
	"\aVILLAGE\x10\x042\xf0\x02\n" +
	"\x0eAddressService\x12Z\n" +
	"\x14GetAddressByLocation\x12 .addr.v1.GetAddressByLocationReq\x1a .addr.v1.GetAddressByLocationRes\x12E\n" +
	"\rGetSubRegions\x12\x19.addr.v1.GetSubRegionsReq\x1a\x19.addr.v1.GetSubRegionsRes\x12A\n" +
	"\aGetList\x12\x1a.addr.v1.GetAddressListReq\x1a\x1a.addr.v1.GetAddressListRes\x128\n" +
	"\x04Save\x12\x17.addr.v1.SaveAddressReq\x1a\x17.addr.v1.SaveAddressRes\x12>\n" +
	"\x06Delete\x12\x19.addr.v1.DeleteAddressReq\x1a\x19.addr.v1.DeleteAddressResB/Z-halalplus/app/user-account-svc/api/addr/v1;v1b\x06proto3"

var (
	file_addr_v1_address_proto_rawDescOnce sync.Once
	file_addr_v1_address_proto_rawDescData []byte
)

func file_addr_v1_address_proto_rawDescGZIP() []byte {
	file_addr_v1_address_proto_rawDescOnce.Do(func() {
		file_addr_v1_address_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_addr_v1_address_proto_rawDesc), len(file_addr_v1_address_proto_rawDesc)))
	})
	return file_addr_v1_address_proto_rawDescData
}

var file_addr_v1_address_proto_enumTypes = make([]protoimpl.EnumInfo, 1)
var file_addr_v1_address_proto_msgTypes = make([]protoimpl.MessageInfo, 15)
var file_addr_v1_address_proto_goTypes = []any{
	(AddressLevel)(0),                   // 0: addr.v1.AddressLevel
	(*GetAddressByLocationReq)(nil),     // 1: addr.v1.GetAddressByLocationReq
	(*GetAddressByLocationResData)(nil), // 2: addr.v1.GetAddressByLocationResData
	(*GetAddressByLocationRes)(nil),     // 3: addr.v1.GetAddressByLocationRes
	(*GetSubRegionsReq)(nil),            // 4: addr.v1.GetSubRegionsReq
	(*AddressItem)(nil),                 // 5: addr.v1.AddressItem
	(*GetSubRegionsResData)(nil),        // 6: addr.v1.GetSubRegionsResData
	(*GetSubRegionsRes)(nil),            // 7: addr.v1.GetSubRegionsRes
	(*UserAddress)(nil),                 // 8: addr.v1.UserAddress
	(*GetAddressListReq)(nil),           // 9: addr.v1.GetAddressListReq
	(*GetAddressListResData)(nil),       // 10: addr.v1.GetAddressListResData
	(*GetAddressListRes)(nil),           // 11: addr.v1.GetAddressListRes
	(*SaveAddressReq)(nil),              // 12: addr.v1.SaveAddressReq
	(*SaveAddressRes)(nil),              // 13: addr.v1.SaveAddressRes
	(*DeleteAddressReq)(nil),            // 14: addr.v1.DeleteAddressReq
	(*DeleteAddressRes)(nil),            // 15: addr.v1.DeleteAddressRes
	(*common.Error)(nil),                // 16: common.Error
	(*wrappers.BoolValue)(nil),          // 17: google.protobuf.BoolValue
}
var file_addr_v1_address_proto_depIdxs = []int32{
	16, // 0: addr.v1.GetAddressByLocationRes.error:type_name -> common.Error
	2,  // 1: addr.v1.GetAddressByLocationRes.data:type_name -> addr.v1.GetAddressByLocationResData
	0,  // 2: addr.v1.AddressItem.level:type_name -> addr.v1.AddressLevel
	5,  // 3: addr.v1.GetSubRegionsResData.list:type_name -> addr.v1.AddressItem
	16, // 4: addr.v1.GetSubRegionsRes.error:type_name -> common.Error
	6,  // 5: addr.v1.GetSubRegionsRes.data:type_name -> addr.v1.GetSubRegionsResData
	17, // 6: addr.v1.GetAddressListReq.is_default:type_name -> google.protobuf.BoolValue
	8,  // 7: addr.v1.GetAddressListResData.list:type_name -> addr.v1.UserAddress
	16, // 8: addr.v1.GetAddressListRes.error:type_name -> common.Error
	10, // 9: addr.v1.GetAddressListRes.data:type_name -> addr.v1.GetAddressListResData
	17, // 10: addr.v1.SaveAddressReq.is_default:type_name -> google.protobuf.BoolValue
	16, // 11: addr.v1.SaveAddressRes.error:type_name -> common.Error
	16, // 12: addr.v1.DeleteAddressRes.error:type_name -> common.Error
	1,  // 13: addr.v1.AddressService.GetAddressByLocation:input_type -> addr.v1.GetAddressByLocationReq
	4,  // 14: addr.v1.AddressService.GetSubRegions:input_type -> addr.v1.GetSubRegionsReq
	9,  // 15: addr.v1.AddressService.GetList:input_type -> addr.v1.GetAddressListReq
	12, // 16: addr.v1.AddressService.Save:input_type -> addr.v1.SaveAddressReq
	14, // 17: addr.v1.AddressService.Delete:input_type -> addr.v1.DeleteAddressReq
	3,  // 18: addr.v1.AddressService.GetAddressByLocation:output_type -> addr.v1.GetAddressByLocationRes
	7,  // 19: addr.v1.AddressService.GetSubRegions:output_type -> addr.v1.GetSubRegionsRes
	11, // 20: addr.v1.AddressService.GetList:output_type -> addr.v1.GetAddressListRes
	13, // 21: addr.v1.AddressService.Save:output_type -> addr.v1.SaveAddressRes
	15, // 22: addr.v1.AddressService.Delete:output_type -> addr.v1.DeleteAddressRes
	18, // [18:23] is the sub-list for method output_type
	13, // [13:18] is the sub-list for method input_type
	13, // [13:13] is the sub-list for extension type_name
	13, // [13:13] is the sub-list for extension extendee
	0,  // [0:13] is the sub-list for field type_name
}

func init() { file_addr_v1_address_proto_init() }
func file_addr_v1_address_proto_init() {
	if File_addr_v1_address_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_addr_v1_address_proto_rawDesc), len(file_addr_v1_address_proto_rawDesc)),
			NumEnums:      1,
			NumMessages:   15,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_addr_v1_address_proto_goTypes,
		DependencyIndexes: file_addr_v1_address_proto_depIdxs,
		EnumInfos:         file_addr_v1_address_proto_enumTypes,
		MessageInfos:      file_addr_v1_address_proto_msgTypes,
	}.Build()
	File_addr_v1_address_proto = out.File
	file_addr_v1_address_proto_goTypes = nil
	file_addr_v1_address_proto_depIdxs = nil
}
