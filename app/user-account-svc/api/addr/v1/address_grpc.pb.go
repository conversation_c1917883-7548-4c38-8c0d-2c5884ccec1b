// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.5.1
// - protoc             v6.31.1
// source: addr/v1/address.proto

package v1

import (
	context "context"

	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.64.0 or later.
const _ = grpc.SupportPackageIsVersion9

const (
	AddressService_GetAddressByLocation_FullMethodName = "/addr.v1.AddressService/GetAddressByLocation"
	AddressService_GetSubRegions_FullMethodName        = "/addr.v1.AddressService/GetSubRegions"
	AddressService_GetList_FullMethodName              = "/addr.v1.AddressService/GetList"
	AddressService_Save_FullMethodName                 = "/addr.v1.AddressService/Save"
	AddressService_Delete_FullMethodName               = "/addr.v1.AddressService/Delete"
)

// AddressServiceClient is the client API for AddressService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
//
// 用户地址服务
// HTTP /api/user-account/addr/v1/AddressService
type AddressServiceClient interface {
	// 根据经纬度查询当前所在地址信息（逆地理编码）
	// GET /api/user-account/addr/v1/AddressService/GetAddressByLocation
	GetAddressByLocation(ctx context.Context, in *GetAddressByLocationReq, opts ...grpc.CallOption) (*GetAddressByLocationRes, error)
	// 查询某一行政区域下的子区域列表
	// 用于四级联动场景，比如传入省份编码，返回该省的所有市
	// 若 parent_code 为空，则返回顶级（省级）区域列表
	// GET /api/user-account/addr/v1/AddressService/GetSubRegions
	GetSubRegions(ctx context.Context, in *GetSubRegionsReq, opts ...grpc.CallOption) (*GetSubRegionsRes, error)
	// 获取当前用户的收货地址列表
	// 可选择仅返回默认地址或全部地址
	// GET /api/user-account/addr/v1/AddressService/GetList
	GetList(ctx context.Context, in *GetAddressListReq, opts ...grpc.CallOption) (*GetAddressListRes, error)
	// 新增或修改用户地址
	// 若请求中的 id 为空或为 0，则新增地址
	// 否则视为更新已有地址信息
	// POST /api/user-account/addr/v1/AddressService/Save
	Save(ctx context.Context, in *SaveAddressReq, opts ...grpc.CallOption) (*SaveAddressRes, error)
	// 删除用户地址
	// 根据地址 ID 删除对应记录，删除后若是默认地址，前端需提示用户设置新的默认地址
	// POST /api/user-account/addr/v1/AddressService/Delete
	Delete(ctx context.Context, in *DeleteAddressReq, opts ...grpc.CallOption) (*DeleteAddressRes, error)
}

type addressServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewAddressServiceClient(cc grpc.ClientConnInterface) AddressServiceClient {
	return &addressServiceClient{cc}
}

func (c *addressServiceClient) GetAddressByLocation(ctx context.Context, in *GetAddressByLocationReq, opts ...grpc.CallOption) (*GetAddressByLocationRes, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GetAddressByLocationRes)
	err := c.cc.Invoke(ctx, AddressService_GetAddressByLocation_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *addressServiceClient) GetSubRegions(ctx context.Context, in *GetSubRegionsReq, opts ...grpc.CallOption) (*GetSubRegionsRes, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GetSubRegionsRes)
	err := c.cc.Invoke(ctx, AddressService_GetSubRegions_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *addressServiceClient) GetList(ctx context.Context, in *GetAddressListReq, opts ...grpc.CallOption) (*GetAddressListRes, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GetAddressListRes)
	err := c.cc.Invoke(ctx, AddressService_GetList_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *addressServiceClient) Save(ctx context.Context, in *SaveAddressReq, opts ...grpc.CallOption) (*SaveAddressRes, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(SaveAddressRes)
	err := c.cc.Invoke(ctx, AddressService_Save_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *addressServiceClient) Delete(ctx context.Context, in *DeleteAddressReq, opts ...grpc.CallOption) (*DeleteAddressRes, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(DeleteAddressRes)
	err := c.cc.Invoke(ctx, AddressService_Delete_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// AddressServiceServer is the server API for AddressService service.
// All implementations must embed UnimplementedAddressServiceServer
// for forward compatibility.
//
// 用户地址服务
// HTTP /api/user-account/addr/v1/AddressService
type AddressServiceServer interface {
	// 根据经纬度查询当前所在地址信息（逆地理编码）
	// GET /api/user-account/addr/v1/AddressService/GetAddressByLocation
	GetAddressByLocation(context.Context, *GetAddressByLocationReq) (*GetAddressByLocationRes, error)
	// 查询某一行政区域下的子区域列表
	// 用于四级联动场景，比如传入省份编码，返回该省的所有市
	// 若 parent_code 为空，则返回顶级（省级）区域列表
	// GET /api/user-account/addr/v1/AddressService/GetSubRegions
	GetSubRegions(context.Context, *GetSubRegionsReq) (*GetSubRegionsRes, error)
	// 获取当前用户的收货地址列表
	// 可选择仅返回默认地址或全部地址
	// GET /api/user-account/addr/v1/AddressService/GetList
	GetList(context.Context, *GetAddressListReq) (*GetAddressListRes, error)
	// 新增或修改用户地址
	// 若请求中的 id 为空或为 0，则新增地址
	// 否则视为更新已有地址信息
	// POST /api/user-account/addr/v1/AddressService/Save
	Save(context.Context, *SaveAddressReq) (*SaveAddressRes, error)
	// 删除用户地址
	// 根据地址 ID 删除对应记录，删除后若是默认地址，前端需提示用户设置新的默认地址
	// POST /api/user-account/addr/v1/AddressService/Delete
	Delete(context.Context, *DeleteAddressReq) (*DeleteAddressRes, error)
	mustEmbedUnimplementedAddressServiceServer()
}

// UnimplementedAddressServiceServer must be embedded to have
// forward compatible implementations.
//
// NOTE: this should be embedded by value instead of pointer to avoid a nil
// pointer dereference when methods are called.
type UnimplementedAddressServiceServer struct{}

func (UnimplementedAddressServiceServer) GetAddressByLocation(context.Context, *GetAddressByLocationReq) (*GetAddressByLocationRes, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetAddressByLocation not implemented")
}
func (UnimplementedAddressServiceServer) GetSubRegions(context.Context, *GetSubRegionsReq) (*GetSubRegionsRes, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetSubRegions not implemented")
}
func (UnimplementedAddressServiceServer) GetList(context.Context, *GetAddressListReq) (*GetAddressListRes, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetList not implemented")
}
func (UnimplementedAddressServiceServer) Save(context.Context, *SaveAddressReq) (*SaveAddressRes, error) {
	return nil, status.Errorf(codes.Unimplemented, "method Save not implemented")
}
func (UnimplementedAddressServiceServer) Delete(context.Context, *DeleteAddressReq) (*DeleteAddressRes, error) {
	return nil, status.Errorf(codes.Unimplemented, "method Delete not implemented")
}
func (UnimplementedAddressServiceServer) mustEmbedUnimplementedAddressServiceServer() {}
func (UnimplementedAddressServiceServer) testEmbeddedByValue()                        {}

// UnsafeAddressServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to AddressServiceServer will
// result in compilation errors.
type UnsafeAddressServiceServer interface {
	mustEmbedUnimplementedAddressServiceServer()
}

func RegisterAddressServiceServer(s grpc.ServiceRegistrar, srv AddressServiceServer) {
	// If the following call pancis, it indicates UnimplementedAddressServiceServer was
	// embedded by pointer and is nil.  This will cause panics if an
	// unimplemented method is ever invoked, so we test this at initialization
	// time to prevent it from happening at runtime later due to I/O.
	if t, ok := srv.(interface{ testEmbeddedByValue() }); ok {
		t.testEmbeddedByValue()
	}
	s.RegisterService(&AddressService_ServiceDesc, srv)
}

func _AddressService_GetAddressByLocation_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetAddressByLocationReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AddressServiceServer).GetAddressByLocation(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: AddressService_GetAddressByLocation_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AddressServiceServer).GetAddressByLocation(ctx, req.(*GetAddressByLocationReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _AddressService_GetSubRegions_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetSubRegionsReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AddressServiceServer).GetSubRegions(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: AddressService_GetSubRegions_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AddressServiceServer).GetSubRegions(ctx, req.(*GetSubRegionsReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _AddressService_GetList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetAddressListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AddressServiceServer).GetList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: AddressService_GetList_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AddressServiceServer).GetList(ctx, req.(*GetAddressListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _AddressService_Save_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SaveAddressReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AddressServiceServer).Save(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: AddressService_Save_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AddressServiceServer).Save(ctx, req.(*SaveAddressReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _AddressService_Delete_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeleteAddressReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AddressServiceServer).Delete(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: AddressService_Delete_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AddressServiceServer).Delete(ctx, req.(*DeleteAddressReq))
	}
	return interceptor(ctx, in, info, handler)
}

// AddressService_ServiceDesc is the grpc.ServiceDesc for AddressService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var AddressService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "addr.v1.AddressService",
	HandlerType: (*AddressServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "GetAddressByLocation",
			Handler:    _AddressService_GetAddressByLocation_Handler,
		},
		{
			MethodName: "GetSubRegions",
			Handler:    _AddressService_GetSubRegions_Handler,
		},
		{
			MethodName: "GetList",
			Handler:    _AddressService_GetList_Handler,
		},
		{
			MethodName: "Save",
			Handler:    _AddressService_Save_Handler,
		},
		{
			MethodName: "Delete",
			Handler:    _AddressService_Delete_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "addr/v1/address.proto",
}
