-- 修改banner、calendar、video相关表的时间字段为bigint毫秒级别
-- 执行前请备份数据库！

-- ========================================
-- Banner相关表修改
-- ========================================

-- 修改banner表的时间字段
ALTER TABLE `banner`
MODIFY COLUMN `created_at` bigint(20) unsigned NOT NULL DEFAULT 0 COMMENT '创建时间(毫秒时间戳)',
MODIFY COLUMN `updated_at` bigint(20) unsigned NOT NULL DEFAULT 0 COMMENT '更新时间(毫秒时间戳)';

-- 修改banner_stats表的时间字段
ALTER TABLE `banner_stats`
MODIFY COLUMN `created_at` bigint(20) unsigned NOT NULL DEFAULT 0 COMMENT '操作时间(毫秒时间戳)';

-- ========================================
-- Calendar相关表修改
-- ========================================

-- 修改calendar_hijriah表的时间字段
ALTER TABLE `calendar_hijriah`
MODIFY COLUMN `created_at` bigint(20) unsigned NOT NULL DEFAULT 0 COMMENT '创建时间(毫秒时间戳)',
MODIFY COLUMN `updated_at` bigint(20) unsigned NOT NULL DEFAULT 0 COMMENT '更新时间(毫秒时间戳)';

-- 修改calendar_events表的时间字段
ALTER TABLE `calendar_events`
MODIFY COLUMN `created_at` bigint(20) unsigned NOT NULL DEFAULT 0 COMMENT '创建时间(毫秒时间戳)',
MODIFY COLUMN `updated_at` bigint(20) unsigned NOT NULL DEFAULT 0 COMMENT '更新时间(毫秒时间戳)';

-- ========================================
-- Video相关表修改
-- ========================================

-- 修改video_categories表的时间字段
ALTER TABLE `video_categories`
MODIFY COLUMN `created_at` bigint(20) unsigned NOT NULL DEFAULT 0 COMMENT '创建时间(毫秒时间戳)',
MODIFY COLUMN `updated_at` bigint(20) unsigned NOT NULL DEFAULT 0 COMMENT '更新时间(毫秒时间戳)';

-- 修改video_category_languages表的时间字段
ALTER TABLE `video_category_languages`
MODIFY COLUMN `created_at` bigint(20) unsigned NOT NULL DEFAULT 0 COMMENT '创建时间(毫秒时间戳)',
MODIFY COLUMN `updated_at` bigint(20) unsigned NOT NULL DEFAULT 0 COMMENT '更新时间(毫秒时间戳)';

-- 修改video_playlists表的时间字段
ALTER TABLE `video_playlists`
MODIFY COLUMN `created_at` bigint(20) unsigned NOT NULL DEFAULT 0 COMMENT '创建时间(毫秒时间戳)',
MODIFY COLUMN `updated_at` bigint(20) unsigned NOT NULL DEFAULT 0 COMMENT '更新时间(毫秒时间戳)';

-- 修改video_playlist_languages表的时间字段
ALTER TABLE `video_playlist_languages`
MODIFY COLUMN `created_at` bigint(20) unsigned NOT NULL DEFAULT 0 COMMENT '创建时间(毫秒时间戳)',
MODIFY COLUMN `updated_at` bigint(20) unsigned NOT NULL DEFAULT 0 COMMENT '更新时间(毫秒时间戳)';

-- 修改videos表的时间字段
ALTER TABLE `videos`
MODIFY COLUMN `created_at` bigint(20) unsigned NOT NULL DEFAULT 0 COMMENT '创建时间(毫秒时间戳)',
MODIFY COLUMN `updated_at` bigint(20) unsigned NOT NULL DEFAULT 0 COMMENT '更新时间(毫秒时间戳)';

-- 修改video_languages表的时间字段
ALTER TABLE `video_languages`
MODIFY COLUMN `created_at` bigint(20) unsigned NOT NULL DEFAULT 0 COMMENT '创建时间(毫秒时间戳)',
MODIFY COLUMN `updated_at` bigint(20) unsigned NOT NULL DEFAULT 0 COMMENT '更新时间(毫秒时间戳)';

-- 修改video_playlist_relations表的时间字段
ALTER TABLE `video_playlist_relations`
MODIFY COLUMN `created_at` bigint(20) unsigned NOT NULL DEFAULT 0 COMMENT '创建时间(毫秒时间戳)',
MODIFY COLUMN `updated_at` bigint(20) unsigned NOT NULL DEFAULT 0 COMMENT '更新时间(毫秒时间戳)';

-- 修改video_collects表的时间字段
ALTER TABLE `video_collects`
MODIFY COLUMN `created_at` bigint(20) unsigned NOT NULL DEFAULT 0 COMMENT '收藏时间(毫秒时间戳)';

-- 修改video_shares表的时间字段
ALTER TABLE `video_shares`
MODIFY COLUMN `created_at` bigint(20) unsigned NOT NULL DEFAULT 0 COMMENT '分享时间(毫秒时间戳)';

-- 修改video_play_history表的时间字段
ALTER TABLE `video_play_history`
MODIFY COLUMN `created_at` bigint(20) unsigned NOT NULL DEFAULT 0 COMMENT '播放时间(毫秒时间戳)',
MODIFY COLUMN `updated_at` bigint(20) unsigned NOT NULL DEFAULT 0 COMMENT '更新时间(毫秒时间戳)';

-- ========================================
-- 修改完成提示
-- ========================================
-- 所有时间字段已修改为bigint(20) unsigned类型，存储毫秒级时间戳
-- 请在应用层代码中相应调整时间处理逻辑