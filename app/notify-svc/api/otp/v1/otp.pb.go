// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        v6.31.1
// source: otp/v1/otp.proto

package otpv1

import (
	common "halalplus/api/common"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"

	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// OTP的类型
type OtpType int32

const (
	OtpType_UNKNOWN               OtpType = 0    // 必须有0值，建议用于未知
	OtpType_REGISTER              OtpType = 1    // 注册
	OtpType_LOGIN                 OtpType = 2    // 登录
	OtpType_BIND                  OtpType = 3    // 绑定
	OtpType_LOGIN_PASSWORD_UPDATE OtpType = 4    // 登录密码修改
	OtpType_PAY_PASSWORD_UPDATE   OtpType = 5    // 支付密码修改
	OtpType_ADD_BANK_CARD         OtpType = 6    // 添加银行卡
	OtpType_ADD_CRYPTO_ADDRESS    OtpType = 7    // 添加虚拟币地址
	OtpType_OTHER                 OtpType = 9999 // 其他
)

// Enum value maps for OtpType.
var (
	OtpType_name = map[int32]string{
		0:    "UNKNOWN",
		1:    "REGISTER",
		2:    "LOGIN",
		3:    "BIND",
		4:    "LOGIN_PASSWORD_UPDATE",
		5:    "PAY_PASSWORD_UPDATE",
		6:    "ADD_BANK_CARD",
		7:    "ADD_CRYPTO_ADDRESS",
		9999: "OTHER",
	}
	OtpType_value = map[string]int32{
		"UNKNOWN":               0,
		"REGISTER":              1,
		"LOGIN":                 2,
		"BIND":                  3,
		"LOGIN_PASSWORD_UPDATE": 4,
		"PAY_PASSWORD_UPDATE":   5,
		"ADD_BANK_CARD":         6,
		"ADD_CRYPTO_ADDRESS":    7,
		"OTHER":                 9999,
	}
)

func (x OtpType) Enum() *OtpType {
	p := new(OtpType)
	*p = x
	return p
}

func (x OtpType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (OtpType) Descriptor() protoreflect.EnumDescriptor {
	return file_otp_v1_otp_proto_enumTypes[0].Descriptor()
}

func (OtpType) Type() protoreflect.EnumType {
	return &file_otp_v1_otp_proto_enumTypes[0]
}

func (x OtpType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use OtpType.Descriptor instead.
func (OtpType) EnumDescriptor() ([]byte, []int) {
	return file_otp_v1_otp_proto_rawDescGZIP(), []int{0}
}

// 短信OTP
type SendSmsReq struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	PhoneInfo     *common.PhoneInfo      `protobuf:"bytes,1,opt,name=phone_info,json=phoneInfo,proto3" json:"phone_info,omitempty"`
	OtpType       OtpType                `protobuf:"varint,2,opt,name=otp_type,json=otpType,proto3,enum=otp.v1.OtpType" json:"otp_type,omitempty"`
	FrontInfo     *common.FrontInfo      `protobuf:"bytes,3,opt,name=front_info,json=frontInfo,proto3" json:"front_info,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *SendSmsReq) Reset() {
	*x = SendSmsReq{}
	mi := &file_otp_v1_otp_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SendSmsReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SendSmsReq) ProtoMessage() {}

func (x *SendSmsReq) ProtoReflect() protoreflect.Message {
	mi := &file_otp_v1_otp_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SendSmsReq.ProtoReflect.Descriptor instead.
func (*SendSmsReq) Descriptor() ([]byte, []int) {
	return file_otp_v1_otp_proto_rawDescGZIP(), []int{0}
}

func (x *SendSmsReq) GetPhoneInfo() *common.PhoneInfo {
	if x != nil {
		return x.PhoneInfo
	}
	return nil
}

func (x *SendSmsReq) GetOtpType() OtpType {
	if x != nil {
		return x.OtpType
	}
	return OtpType_UNKNOWN
}

func (x *SendSmsReq) GetFrontInfo() *common.FrontInfo {
	if x != nil {
		return x.FrontInfo
	}
	return nil
}

type SendSmsRes struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Code          int32                  `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`
	Msg           string                 `protobuf:"bytes,2,opt,name=msg,proto3" json:"msg,omitempty"`
	Error         *common.Error          `protobuf:"bytes,3,opt,name=error,proto3" json:"error,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *SendSmsRes) Reset() {
	*x = SendSmsRes{}
	mi := &file_otp_v1_otp_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SendSmsRes) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SendSmsRes) ProtoMessage() {}

func (x *SendSmsRes) ProtoReflect() protoreflect.Message {
	mi := &file_otp_v1_otp_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SendSmsRes.ProtoReflect.Descriptor instead.
func (*SendSmsRes) Descriptor() ([]byte, []int) {
	return file_otp_v1_otp_proto_rawDescGZIP(), []int{1}
}

func (x *SendSmsRes) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *SendSmsRes) GetMsg() string {
	if x != nil {
		return x.Msg
	}
	return ""
}

func (x *SendSmsRes) GetError() *common.Error {
	if x != nil {
		return x.Error
	}
	return nil
}

type VerifyReq struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Otp           string                 `protobuf:"bytes,1,opt,name=otp,proto3" json:"otp,omitempty"`
	OtpType       OtpType                `protobuf:"varint,2,opt,name=otp_type,json=otpType,proto3,enum=otp.v1.OtpType" json:"otp_type,omitempty"`
	FrontInfo     *common.FrontInfo      `protobuf:"bytes,3,opt,name=front_info,json=frontInfo,proto3" json:"front_info,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *VerifyReq) Reset() {
	*x = VerifyReq{}
	mi := &file_otp_v1_otp_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *VerifyReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*VerifyReq) ProtoMessage() {}

func (x *VerifyReq) ProtoReflect() protoreflect.Message {
	mi := &file_otp_v1_otp_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use VerifyReq.ProtoReflect.Descriptor instead.
func (*VerifyReq) Descriptor() ([]byte, []int) {
	return file_otp_v1_otp_proto_rawDescGZIP(), []int{2}
}

func (x *VerifyReq) GetOtp() string {
	if x != nil {
		return x.Otp
	}
	return ""
}

func (x *VerifyReq) GetOtpType() OtpType {
	if x != nil {
		return x.OtpType
	}
	return OtpType_UNKNOWN
}

func (x *VerifyReq) GetFrontInfo() *common.FrontInfo {
	if x != nil {
		return x.FrontInfo
	}
	return nil
}

type VerifyRes struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Code          int32                  `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`
	Msg           string                 `protobuf:"bytes,2,opt,name=msg,proto3" json:"msg,omitempty"`
	Error         *common.Error          `protobuf:"bytes,3,opt,name=error,proto3" json:"error,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *VerifyRes) Reset() {
	*x = VerifyRes{}
	mi := &file_otp_v1_otp_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *VerifyRes) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*VerifyRes) ProtoMessage() {}

func (x *VerifyRes) ProtoReflect() protoreflect.Message {
	mi := &file_otp_v1_otp_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use VerifyRes.ProtoReflect.Descriptor instead.
func (*VerifyRes) Descriptor() ([]byte, []int) {
	return file_otp_v1_otp_proto_rawDescGZIP(), []int{3}
}

func (x *VerifyRes) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *VerifyRes) GetMsg() string {
	if x != nil {
		return x.Msg
	}
	return ""
}

func (x *VerifyRes) GetError() *common.Error {
	if x != nil {
		return x.Error
	}
	return nil
}

var File_otp_v1_otp_proto protoreflect.FileDescriptor

const file_otp_v1_otp_proto_rawDesc = "" +
	"\n" +
	"\x10otp/v1/otp.proto\x12\x06otp.v1\x1a\x17common/front_info.proto\x1a\x11common/base.proto\x1a\x17common/phone_info.proto\"\x9c\x01\n" +
	"\n" +
	"SendSmsReq\x120\n" +
	"\n" +
	"phone_info\x18\x01 \x01(\v2\x11.common.PhoneInfoR\tphoneInfo\x12*\n" +
	"\botp_type\x18\x02 \x01(\x0e2\x0f.otp.v1.OtpTypeR\aotpType\x120\n" +
	"\n" +
	"front_info\x18\x03 \x01(\v2\x11.common.FrontInfoR\tfrontInfo\"W\n" +
	"\n" +
	"SendSmsRes\x12\x12\n" +
	"\x04code\x18\x01 \x01(\x05R\x04code\x12\x10\n" +
	"\x03msg\x18\x02 \x01(\tR\x03msg\x12#\n" +
	"\x05error\x18\x03 \x01(\v2\r.common.ErrorR\x05error\"{\n" +
	"\tVerifyReq\x12\x10\n" +
	"\x03otp\x18\x01 \x01(\tR\x03otp\x12*\n" +
	"\botp_type\x18\x02 \x01(\x0e2\x0f.otp.v1.OtpTypeR\aotpType\x120\n" +
	"\n" +
	"front_info\x18\x03 \x01(\v2\x11.common.FrontInfoR\tfrontInfo\"V\n" +
	"\tVerifyRes\x12\x12\n" +
	"\x04code\x18\x01 \x01(\x05R\x04code\x12\x10\n" +
	"\x03msg\x18\x02 \x01(\tR\x03msg\x12#\n" +
	"\x05error\x18\x03 \x01(\v2\r.common.ErrorR\x05error*\xa4\x01\n" +
	"\aOtpType\x12\v\n" +
	"\aUNKNOWN\x10\x00\x12\f\n" +
	"\bREGISTER\x10\x01\x12\t\n" +
	"\x05LOGIN\x10\x02\x12\b\n" +
	"\x04BIND\x10\x03\x12\x19\n" +
	"\x15LOGIN_PASSWORD_UPDATE\x10\x04\x12\x17\n" +
	"\x13PAY_PASSWORD_UPDATE\x10\x05\x12\x11\n" +
	"\rADD_BANK_CARD\x10\x06\x12\x16\n" +
	"\x12ADD_CRYPTO_ADDRESS\x10\a\x12\n" +
	"\n" +
	"\x05OTHER\x10\x8fN2o\n" +
	"\n" +
	"OtpService\x12.\n" +
	"\x06Verify\x12\x11.otp.v1.VerifyReq\x1a\x11.otp.v1.VerifyRes\x121\n" +
	"\aSendSms\x12\x12.otp.v1.SendSmsReq\x1a\x12.otp.v1.SendSmsResB+Z)halalplus/app/notify-svc/api/otp/v1;otpv1b\x06proto3"

var (
	file_otp_v1_otp_proto_rawDescOnce sync.Once
	file_otp_v1_otp_proto_rawDescData []byte
)

func file_otp_v1_otp_proto_rawDescGZIP() []byte {
	file_otp_v1_otp_proto_rawDescOnce.Do(func() {
		file_otp_v1_otp_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_otp_v1_otp_proto_rawDesc), len(file_otp_v1_otp_proto_rawDesc)))
	})
	return file_otp_v1_otp_proto_rawDescData
}

var file_otp_v1_otp_proto_enumTypes = make([]protoimpl.EnumInfo, 1)
var file_otp_v1_otp_proto_msgTypes = make([]protoimpl.MessageInfo, 4)
var file_otp_v1_otp_proto_goTypes = []any{
	(OtpType)(0),             // 0: otp.v1.OtpType
	(*SendSmsReq)(nil),       // 1: otp.v1.SendSmsReq
	(*SendSmsRes)(nil),       // 2: otp.v1.SendSmsRes
	(*VerifyReq)(nil),        // 3: otp.v1.VerifyReq
	(*VerifyRes)(nil),        // 4: otp.v1.VerifyRes
	(*common.PhoneInfo)(nil), // 5: common.PhoneInfo
	(*common.FrontInfo)(nil), // 6: common.FrontInfo
	(*common.Error)(nil),     // 7: common.Error
}
var file_otp_v1_otp_proto_depIdxs = []int32{
	5, // 0: otp.v1.SendSmsReq.phone_info:type_name -> common.PhoneInfo
	0, // 1: otp.v1.SendSmsReq.otp_type:type_name -> otp.v1.OtpType
	6, // 2: otp.v1.SendSmsReq.front_info:type_name -> common.FrontInfo
	7, // 3: otp.v1.SendSmsRes.error:type_name -> common.Error
	0, // 4: otp.v1.VerifyReq.otp_type:type_name -> otp.v1.OtpType
	6, // 5: otp.v1.VerifyReq.front_info:type_name -> common.FrontInfo
	7, // 6: otp.v1.VerifyRes.error:type_name -> common.Error
	3, // 7: otp.v1.OtpService.Verify:input_type -> otp.v1.VerifyReq
	1, // 8: otp.v1.OtpService.SendSms:input_type -> otp.v1.SendSmsReq
	4, // 9: otp.v1.OtpService.Verify:output_type -> otp.v1.VerifyRes
	2, // 10: otp.v1.OtpService.SendSms:output_type -> otp.v1.SendSmsRes
	9, // [9:11] is the sub-list for method output_type
	7, // [7:9] is the sub-list for method input_type
	7, // [7:7] is the sub-list for extension type_name
	7, // [7:7] is the sub-list for extension extendee
	0, // [0:7] is the sub-list for field type_name
}

func init() { file_otp_v1_otp_proto_init() }
func file_otp_v1_otp_proto_init() {
	if File_otp_v1_otp_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_otp_v1_otp_proto_rawDesc), len(file_otp_v1_otp_proto_rawDesc)),
			NumEnums:      1,
			NumMessages:   4,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_otp_v1_otp_proto_goTypes,
		DependencyIndexes: file_otp_v1_otp_proto_depIdxs,
		EnumInfos:         file_otp_v1_otp_proto_enumTypes,
		MessageInfos:      file_otp_v1_otp_proto_msgTypes,
	}.Build()
	File_otp_v1_otp_proto = out.File
	file_otp_v1_otp_proto_goTypes = nil
	file_otp_v1_otp_proto_depIdxs = nil
}
