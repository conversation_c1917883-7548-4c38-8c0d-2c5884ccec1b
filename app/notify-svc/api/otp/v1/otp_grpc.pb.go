// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.5.1
// - protoc             v6.31.1
// source: otp/v1/otp.proto

package otpv1

import (
	context "context"

	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.64.0 or later.
const _ = grpc.SupportPackageIsVersion9

const (
	OtpService_Verify_FullMethodName  = "/otp.v1.OtpService/Verify"
	OtpService_SendSms_FullMethodName = "/otp.v1.OtpService/SendSms"
)

// OtpServiceClient is the client API for OtpService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type OtpServiceClient interface {
	// 检查OTP
	Verify(ctx context.Context, in *VerifyReq, opts ...grpc.CallOption) (*VerifyRes, error)
	// 发送短信验证码
	// POST /api/notify/otp/v1/OtpService/SendSms
	SendSms(ctx context.Context, in *SendSmsReq, opts ...grpc.CallOption) (*SendSmsRes, error)
}

type otpServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewOtpServiceClient(cc grpc.ClientConnInterface) OtpServiceClient {
	return &otpServiceClient{cc}
}

func (c *otpServiceClient) Verify(ctx context.Context, in *VerifyReq, opts ...grpc.CallOption) (*VerifyRes, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(VerifyRes)
	err := c.cc.Invoke(ctx, OtpService_Verify_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *otpServiceClient) SendSms(ctx context.Context, in *SendSmsReq, opts ...grpc.CallOption) (*SendSmsRes, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(SendSmsRes)
	err := c.cc.Invoke(ctx, OtpService_SendSms_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// OtpServiceServer is the server API for OtpService service.
// All implementations must embed UnimplementedOtpServiceServer
// for forward compatibility.
type OtpServiceServer interface {
	// 检查OTP
	Verify(context.Context, *VerifyReq) (*VerifyRes, error)
	// 发送短信验证码
	// POST /api/notify/otp/v1/OtpService/SendSms
	SendSms(context.Context, *SendSmsReq) (*SendSmsRes, error)
	mustEmbedUnimplementedOtpServiceServer()
}

// UnimplementedOtpServiceServer must be embedded to have
// forward compatible implementations.
//
// NOTE: this should be embedded by value instead of pointer to avoid a nil
// pointer dereference when methods are called.
type UnimplementedOtpServiceServer struct{}

func (UnimplementedOtpServiceServer) Verify(context.Context, *VerifyReq) (*VerifyRes, error) {
	return nil, status.Errorf(codes.Unimplemented, "method Verify not implemented")
}
func (UnimplementedOtpServiceServer) SendSms(context.Context, *SendSmsReq) (*SendSmsRes, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SendSms not implemented")
}
func (UnimplementedOtpServiceServer) mustEmbedUnimplementedOtpServiceServer() {}
func (UnimplementedOtpServiceServer) testEmbeddedByValue()                    {}

// UnsafeOtpServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to OtpServiceServer will
// result in compilation errors.
type UnsafeOtpServiceServer interface {
	mustEmbedUnimplementedOtpServiceServer()
}

func RegisterOtpServiceServer(s grpc.ServiceRegistrar, srv OtpServiceServer) {
	// If the following call pancis, it indicates UnimplementedOtpServiceServer was
	// embedded by pointer and is nil.  This will cause panics if an
	// unimplemented method is ever invoked, so we test this at initialization
	// time to prevent it from happening at runtime later due to I/O.
	if t, ok := srv.(interface{ testEmbeddedByValue() }); ok {
		t.testEmbeddedByValue()
	}
	s.RegisterService(&OtpService_ServiceDesc, srv)
}

func _OtpService_Verify_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(VerifyReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(OtpServiceServer).Verify(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: OtpService_Verify_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(OtpServiceServer).Verify(ctx, req.(*VerifyReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _OtpService_SendSms_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SendSmsReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(OtpServiceServer).SendSms(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: OtpService_SendSms_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(OtpServiceServer).SendSms(ctx, req.(*SendSmsReq))
	}
	return interceptor(ctx, in, info, handler)
}

// OtpService_ServiceDesc is the grpc.ServiceDesc for OtpService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var OtpService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "otp.v1.OtpService",
	HandlerType: (*OtpServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "Verify",
			Handler:    _OtpService_Verify_Handler,
		},
		{
			MethodName: "SendSms",
			Handler:    _OtpService_SendSms_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "otp/v1/otp.proto",
}
